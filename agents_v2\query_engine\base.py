import os
import sys
import json
import asyncio
import aiofiles
from pathlib import Path
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import openai
from dotenv import load_dotenv

# Add parent directory to path to import other agents
sys.path.append(str(Path(__file__).parent.parent))

# Import our agents
from latticeheader_agent.base import extract_lattice_headers, print_results as print_header_results
from hybridrag.base import run_hybrid_rag_pipeline, generate_answer

# Load environment variables
load_dotenv()

# OpenAI client configuration
client = openai.OpenAI(
    api_key=os.getenv("AGENTIC_API_KEY"),
    base_url=os.getenv("AGENTIC_BASE_URL")
)

# Cache directory for query engine data
CACHE_DIR = Path(__file__).parent.parent / "cached_data"
CACHE_DIR.mkdir(exist_ok=True)

class QueryEngine:
    """
    Central Query Engine that orchestrates the entire document processing pipeline.
    Manages the flow between Document Processing, Lattice Header Agent, and Hybrid RAG.
    """
    
    def __init__(self):
        self.lattice_headers = None
        self.lattice_headers_available = False
        self.processed_documents = {}
        self.rag_pipelines = {}
        self.document_store = {}
        
    async def initialize_with_documents(self, pdf_paths: List[str]) -> Dict[str, Any]:
        """
        Initialize the Query Engine with a collection of documents.
        This follows the flow: Documents -> Process Endpoint -> Lattice Headers Check
        """
        print("🚀 QUERY ENGINE INITIALIZATION")
        print("=" * 60)
        print(f"📂 Processing {len(pdf_paths)} documents...")
        
        # Validate document paths
        valid_paths = []
        for pdf_path in pdf_paths:
            if Path(pdf_path).exists():
                valid_paths.append(pdf_path)
                self.document_store[pdf_path] = {
                    "filename": Path(pdf_path).name,
                    "path": pdf_path,
                    "processed": False
                }
            else:
                print(f"⚠️  Document not found: {pdf_path}")
        
        if not valid_paths:
            return {
                "success": False,
                "error": "No valid documents found",
                "lattice_headers_available": False
            }
        
        print(f"✅ Found {len(valid_paths)} valid documents")
        
        # STEP 1: Extract Lattice Headers (Header Generation Agent)
        print("\n🏷️  STEP 1: LATTICE HEADER EXTRACTION")
        print("-" * 50)
        
        header_results = await extract_lattice_headers(valid_paths)
        
        # Check if we have successful lattice headers
        if header_results["final_headers_result"]["success"]:
            self.lattice_headers = header_results["final_headers_result"]["final_headers"]
            self.lattice_headers_available = True
            
            print(f"✅ Lattice headers extracted successfully!")
            print(f"📊 Final headers count: {len(self.lattice_headers)}")
            print("🏷️  Headers:", ", ".join(self.lattice_headers[:5]) + ("..." if len(self.lattice_headers) > 5 else ""))
        else:
            print("❌ Failed to extract lattice headers")
            self.lattice_headers_available = False
        
        # STEP 2: Initialize RAG pipelines for each document
        print("\n🔍 STEP 2: INITIALIZING RAG PIPELINES")
        print("-" * 50)
        
        for pdf_path in valid_paths:
            try:
                print(f"📄 Initializing RAG for {Path(pdf_path).name}...")
                query_rag, doc_info = await run_hybrid_rag_pipeline(pdf_path)
                
                self.rag_pipelines[pdf_path] = query_rag
                self.document_store[pdf_path].update({
                    "processed": True,
                    "doc_info": doc_info,
                    "rag_available": True
                })
                
                print(f"✅ RAG pipeline ready for {doc_info['file_name']}")
                
            except Exception as e:
                print(f"❌ Failed to initialize RAG for {Path(pdf_path).name}: {e}")
                self.document_store[pdf_path]["rag_available"] = False
        
        successful_rags = sum(1 for doc in self.document_store.values() if doc.get("rag_available", False))
        
        print(f"\n🎯 INITIALIZATION COMPLETE")
        print(f"✅ Lattice Headers: {'Available' if self.lattice_headers_available else 'Not Available'}")
        print(f"✅ RAG Pipelines: {successful_rags}/{len(valid_paths)} ready")
        
        return {
            "success": True,
            "lattice_headers_available": self.lattice_headers_available,
            "lattice_headers": self.lattice_headers,
            "documents_processed": len(valid_paths),
            "rag_pipelines_ready": successful_rags,
            "header_extraction_results": header_results
        }
    
    async def process_query(self, user_query: str, query_type: str = "auto") -> Dict[str, Any]:
        """
        Process a user query through the appropriate pipeline.
        
        Args:
            user_query: The user's question
            query_type: "lattice", "rag", or "auto" (auto-detect)
        """
        print(f"\n🔍 PROCESSING QUERY: {user_query}")
        print("=" * 60)
        
        # Determine query type if auto
        if query_type == "auto":
            query_type = await self._determine_query_type(user_query)
        
        print(f"📋 Query Type: {query_type.upper()}")
        
        if query_type == "lattice" and self.lattice_headers_available:
            return await self._process_lattice_query(user_query)
        elif query_type == "rag":
            return await self._process_rag_query(user_query)
        else:
            # Fallback to RAG if lattice not available
            print("⚠️  Lattice headers not available, falling back to RAG")
            return await self._process_rag_query(user_query)
    
    async def _determine_query_type(self, user_query: str) -> str:
        """
        Determine if query should use lattice extraction or RAG search.
        """
        # Simple heuristics - can be enhanced with LLM classification
        lattice_keywords = [
            "extract", "data", "table", "value", "amount", "number", 
            "revenue", "income", "date", "financial", "metrics", "kpi"
        ]
        
        query_lower = user_query.lower()
        
        # If lattice headers available and query seems structured
        if self.lattice_headers_available:
            if any(keyword in query_lower for keyword in lattice_keywords):
                return "lattice"
        
        return "rag"
    
    async def _process_lattice_query(self, user_query: str) -> Dict[str, Any]:
        """
        Process query using lattice extraction across all documents.
        This follows: Query Engine -> Lattice Agent -> Document answers -> Complete Lattice
        """
        print("🏗️  LATTICE PROCESSING MODE")
        print("-" * 40)
        
        if not self.lattice_headers_available:
            return {
                "success": False,
                "error": "Lattice headers not available",
                "query_type": "lattice"
            }
        
        # Extract data from each document using lattice headers
        lattice_results = []
        
        for pdf_path, doc_info in self.document_store.items():
            if not doc_info.get("processed", False):
                continue
                
            print(f"📄 Extracting lattice data from {doc_info['filename']}...")
            
            try:
                # Use RAG to get relevant content, then extract lattice data
                if pdf_path in self.rag_pipelines:
                    rag_query = self.rag_pipelines[pdf_path]
                    retrieved_chunks = rag_query(user_query)
                    
                    # Extract lattice data from retrieved content
                    lattice_data = await self._extract_lattice_data_from_chunks(
                        retrieved_chunks, self.lattice_headers, doc_info['filename']
                    )
                    
                    lattice_results.append({
                        "document": doc_info['filename'],
                        "path": pdf_path,
                        "lattice_data": lattice_data,
                        "success": True
                    })
                    
                    print(f"✅ Lattice extraction complete for {doc_info['filename']}")
                
            except Exception as e:
                print(f"❌ Error processing {doc_info['filename']}: {e}")
                lattice_results.append({
                    "document": doc_info['filename'],
                    "path": pdf_path,
                    "lattice_data": {},
                    "success": False,
                    "error": str(e)
                })
        
        # Compile complete lattice response
        complete_lattice = await self._compile_complete_lattice(lattice_results, user_query)
        
        return {
            "success": True,
            "query_type": "lattice",
            "query": user_query,
            "lattice_headers": self.lattice_headers,
            "individual_results": lattice_results,
            "complete_lattice": complete_lattice,
            "documents_processed": len(lattice_results)
        }
    
    async def _process_rag_query(self, user_query: str) -> Dict[str, Any]:
        """
        Process query using RAG search across documents.
        """
        print("🔍 RAG PROCESSING MODE")
        print("-" * 40)
        
        rag_results = []
        
        for pdf_path, doc_info in self.document_store.items():
            if not doc_info.get("rag_available", False):
                continue
                
            print(f"📄 Querying {doc_info['filename']} with RAG...")
            
            try:
                rag_query = self.rag_pipelines[pdf_path]
                retrieved_chunks = rag_query(user_query)
                answer = generate_answer(user_query, retrieved_chunks)
                
                rag_results.append({
                    "document": doc_info['filename'],
                    "path": pdf_path,
                    "answer": answer,
                    "retrieved_chunks": len(retrieved_chunks),
                    "success": True
                })
                
                print(f"✅ RAG query complete for {doc_info['filename']}")
                
            except Exception as e:
                print(f"❌ Error querying {doc_info['filename']}: {e}")
                rag_results.append({
                    "document": doc_info['filename'],
                    "path": pdf_path,
                    "answer": None,
                    "success": False,
                    "error": str(e)
                })
        
        # Synthesize final answer from all documents
        final_answer = await self._synthesize_rag_answers(rag_results, user_query)
        
        return {
            "success": True,
            "query_type": "rag",
            "query": user_query,
            "individual_results": rag_results,
            "final_answer": final_answer,
            "documents_queried": len(rag_results)
        }

    async def _extract_lattice_data_from_chunks(self, retrieved_chunks: List, headers: List[str], filename: str) -> Dict[str, Any]:
        """
        Extract structured lattice data from retrieved chunks using the predefined headers.
        """
        print(f"🏗️  Extracting lattice data from {filename}...")

        # Combine retrieved chunks into context
        context = "\n\n".join([chunk for _, chunk in retrieved_chunks])

        # Create extraction prompt
        extraction_prompt = f"""
You are an expert data extraction specialist. Your task is to extract specific data points from the provided document content using the given lattice headers.

LATTICE HEADERS TO EXTRACT:
{json.dumps(headers, indent=2)}

DOCUMENT CONTENT:
{context}

INSTRUCTIONS:
1. For each lattice header, extract the corresponding value from the document content
2. If a value is not found or not applicable, use "N/A"
3. Be precise and extract exact values when possible
4. For financial figures, include currency symbols and units
5. For dates, use consistent formatting (YYYY-MM-DD when possible)
6. For percentages, include the % symbol

CRITICAL: You must respond with ONLY a valid JSON object in this exact format:
{{
    "Header 1": "extracted_value_1",
    "Header 2": "extracted_value_2",
    ...
}}

No other text, explanations, or formatting. Just the JSON with extracted values.
"""

        try:
            response = await asyncio.to_thread(
                client.chat.completions.create,
                model="agentic-large",
                messages=[
                    {"role": "system", "content": "You are a professional data extraction specialist. Always respond with valid JSON only."},
                    {"role": "user", "content": extraction_prompt}
                ],
                temperature=0.1,  # Low temperature for consistent extraction
                max_tokens=2000
            )

            response_text = response.choices[0].message.content.strip()

            # Parse JSON response
            try:
                extracted_data = json.loads(response_text)
                print(f"✅ Extracted {len(extracted_data)} data points from {filename}")
                return extracted_data

            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing error for {filename}: {e}")
                return {header: "N/A" for header in headers}

        except Exception as e:
            print(f"❌ Error extracting lattice data from {filename}: {e}")
            return {header: "N/A" for header in headers}

    async def _compile_complete_lattice(self, lattice_results: List[Dict], user_query: str) -> Dict[str, Any]:
        """
        Compile all individual lattice results into a complete lattice response.
        """
        print("🧩 Compiling complete lattice response...")

        # Organize data by headers across all documents
        compiled_data = {}
        successful_docs = []

        for result in lattice_results:
            if result["success"]:
                successful_docs.append(result["document"])
                for header, value in result["lattice_data"].items():
                    if header not in compiled_data:
                        compiled_data[header] = {}
                    compiled_data[header][result["document"]] = value

        # Generate summary and insights
        summary_prompt = f"""
You are a business analyst reviewing extracted data from multiple documents.

USER QUERY: {user_query}

EXTRACTED LATTICE DATA:
{json.dumps(compiled_data, indent=2)}

DOCUMENTS ANALYZED: {', '.join(successful_docs)}

INSTRUCTIONS:
1. Provide a comprehensive analysis of the extracted data
2. Answer the user's specific query based on the lattice data
3. Highlight key insights, trends, or patterns across documents
4. Include specific values and comparisons where relevant
5. If data is missing or inconsistent, mention it

Provide a clear, professional analysis that directly addresses the user's query.
"""

        try:
            response = await asyncio.to_thread(
                client.chat.completions.create,
                model="agentic-large",
                messages=[
                    {"role": "system", "content": "You are a professional business analyst providing data-driven insights."},
                    {"role": "user", "content": summary_prompt}
                ],
                temperature=0.3,
                max_tokens=3000
            )

            analysis = response.choices[0].message.content

            return {
                "compiled_data": compiled_data,
                "analysis": analysis,
                "documents_included": successful_docs,
                "total_headers": len(compiled_data),
                "success": True
            }

        except Exception as e:
            print(f"❌ Error compiling lattice response: {e}")
            return {
                "compiled_data": compiled_data,
                "analysis": "Error generating analysis",
                "documents_included": successful_docs,
                "total_headers": len(compiled_data),
                "success": False,
                "error": str(e)
            }

    async def _synthesize_rag_answers(self, rag_results: List[Dict], user_query: str) -> str:
        """
        Synthesize individual RAG answers into a comprehensive final answer.
        """
        print("🔗 Synthesizing RAG answers...")

        # Collect successful answers
        successful_answers = []
        for result in rag_results:
            if result["success"] and result["answer"]:
                successful_answers.append({
                    "document": result["document"],
                    "answer": result["answer"]
                })

        if not successful_answers:
            return "No answers could be generated from the available documents."

        # Create synthesis prompt
        synthesis_prompt = f"""
You are an expert analyst tasked with synthesizing information from multiple document analyses.

USER QUERY: {user_query}

INDIVIDUAL DOCUMENT ANSWERS:
"""

        for i, answer_data in enumerate(successful_answers, 1):
            synthesis_prompt += f"\n--- Document {i}: {answer_data['document']} ---\n{answer_data['answer']}\n"

        synthesis_prompt += f"""

INSTRUCTIONS:
1. Synthesize the information from all document answers into a comprehensive response
2. Directly address the user's query: "{user_query}"
3. Highlight agreements and differences between documents
4. Provide specific examples and evidence from the documents
5. Organize the response logically and professionally
6. If documents contradict each other, mention this explicitly

Provide a clear, comprehensive answer that leverages insights from all analyzed documents.
"""

        try:
            response = await asyncio.to_thread(
                client.chat.completions.create,
                model="agentic-large",
                messages=[
                    {"role": "system", "content": "You are a professional analyst synthesizing information from multiple sources."},
                    {"role": "user", "content": synthesis_prompt}
                ],
                temperature=0.4,
                max_tokens=4000
            )

            return response.choices[0].message.content

        except Exception as e:
            print(f"❌ Error synthesizing answers: {e}")
            return f"Error synthesizing answers from {len(successful_answers)} documents."

    def get_status(self) -> Dict[str, Any]:
        """Get current status of the Query Engine."""
        return {
            "lattice_headers_available": self.lattice_headers_available,
            "lattice_headers_count": len(self.lattice_headers) if self.lattice_headers else 0,
            "documents_loaded": len(self.document_store),
            "rag_pipelines_ready": len(self.rag_pipelines),
            "documents": {
                path: {
                    "filename": info["filename"],
                    "processed": info.get("processed", False),
                    "rag_available": info.get("rag_available", False)
                }
                for path, info in self.document_store.items()
            }
        }

def print_query_results(results: Dict[str, Any]):
    """Print query results in a formatted way."""
    print("\n" + "=" * 60)
    print("🎯 QUERY RESULTS")
    print("=" * 60)

    query_type = results.get("query_type", "unknown")
    print(f"📋 Query Type: {query_type.upper()}")
    print(f"❓ Query: {results.get('query', 'N/A')}")
    print(f"✅ Success: {results.get('success', False)}")

    if not results.get("success", False):
        print(f"❌ Error: {results.get('error', 'Unknown error')}")
        return

    if query_type == "lattice":
        print_lattice_results(results)
    elif query_type == "rag":
        print_rag_results(results)

    print("=" * 60)

def print_lattice_results(results: Dict[str, Any]):
    """Print lattice query results."""
    print(f"\n🏗️  LATTICE EXTRACTION RESULTS")
    print(f"📊 Documents Processed: {results.get('documents_processed', 0)}")
    print(f"🏷️  Headers Used: {len(results.get('lattice_headers', []))}")

    # Print individual document results
    print(f"\n📄 INDIVIDUAL DOCUMENT RESULTS:")
    for result in results.get("individual_results", []):
        print(f"\n--- {result['document']} ---")
        if result["success"]:
            lattice_data = result["lattice_data"]
            for header, value in lattice_data.items():
                print(f"  {header}: {value}")
        else:
            print(f"  ❌ Error: {result.get('error', 'Unknown error')}")

    # Print complete lattice analysis
    complete_lattice = results.get("complete_lattice", {})
    if complete_lattice.get("success", False):
        print(f"\n🧩 COMPLETE LATTICE ANALYSIS:")
        print("-" * 40)
        print(complete_lattice.get("analysis", "No analysis available"))

def print_rag_results(results: Dict[str, Any]):
    """Print RAG query results."""
    print(f"\n🔍 RAG SEARCH RESULTS")
    print(f"📊 Documents Queried: {results.get('documents_queried', 0)}")

    # Print individual document answers
    print(f"\n📄 INDIVIDUAL DOCUMENT ANSWERS:")
    for result in results.get("individual_results", []):
        print(f"\n--- {result['document']} ---")
        if result["success"]:
            answer = result["answer"]
            # Truncate long answers for display
            if len(answer) > 300:
                print(f"  {answer[:300]}...")
            else:
                print(f"  {answer}")
        else:
            print(f"  ❌ Error: {result.get('error', 'Unknown error')}")

    # Print final synthesized answer
    final_answer = results.get("final_answer", "")
    if final_answer:
        print(f"\n🎯 FINAL SYNTHESIZED ANSWER:")
        print("-" * 40)
        print(final_answer)

async def interactive_query_session(query_engine: QueryEngine):
    """Run an interactive query session with the Query Engine."""
    print("\n🤖 INTERACTIVE QUERY SESSION")
    print("=" * 60)
    print("Commands:")
    print("  - Type your question to query the documents")
    print("  - 'status' to see engine status")
    print("  - 'lattice' to force lattice mode")
    print("  - 'rag' to force RAG mode")
    print("  - 'quit' or 'q' to exit")
    print("=" * 60)

    query_count = 0

    while True:
        try:
            user_input = input("\n🔍 Enter your query: ").strip()

            if user_input.lower() in ['quit', 'q', 'exit']:
                print(f"\n👋 Session ended. Processed {query_count} queries.")
                break

            if user_input.lower() == 'status':
                status = query_engine.get_status()
                print("\n📊 QUERY ENGINE STATUS:")
                print(f"  Lattice Headers: {'✅ Available' if status['lattice_headers_available'] else '❌ Not Available'}")
                print(f"  Headers Count: {status['lattice_headers_count']}")
                print(f"  Documents Loaded: {status['documents_loaded']}")
                print(f"  RAG Pipelines Ready: {status['rag_pipelines_ready']}")
                continue

            if not user_input:
                print("⚠️  Please enter a query.")
                continue

            # Determine query type
            query_type = "auto"
            if user_input.lower().startswith("lattice:"):
                query_type = "lattice"
                user_input = user_input[8:].strip()
            elif user_input.lower().startswith("rag:"):
                query_type = "rag"
                user_input = user_input[4:].strip()

            query_count += 1
            print(f"\n⏳ Processing query #{query_count}...")

            # Process the query
            results = await query_engine.process_query(user_input, query_type)

            # Display results
            print_query_results(results)

        except KeyboardInterrupt:
            print(f"\n\n👋 Session interrupted. Processed {query_count} queries.")
            break
        except Exception as e:
            print(f"\n❌ Error processing query: {e}")

async def main():
    """Main execution function for the Query Engine."""
    print("🚀 QUANTERA QUERY ENGINE")
    print("=" * 60)

    # Example document paths - update these for your use case
    sample_pdf_paths = [
        "../../mp_materials/pdfs/form-10-k.pdf",
        "../../mp_materials/pdfs/form-10-q.pdf",
        "../../mp_materials/pdfs/contract.pdf",
        "../../resido/pdfs/form-10-k.pdf",
        "../../resido/pdfs/annual-reports.pdf",
        "../../resido/pdfs/environmental-agreement.pdf",
    ]

    # Initialize Query Engine
    query_engine = QueryEngine()

    try:
        # Initialize with documents
        init_results = await query_engine.initialize_with_documents(sample_pdf_paths)

        if not init_results["success"]:
            print(f"❌ Failed to initialize Query Engine: {init_results.get('error', 'Unknown error')}")
            return

        print(f"\n✅ Query Engine initialized successfully!")
        print(f"📊 Lattice Headers: {'Available' if init_results['lattice_headers_available'] else 'Not Available'}")
        print(f"📄 Documents Processed: {init_results['documents_processed']}")
        print(f"🔍 RAG Pipelines Ready: {init_results['rag_pipelines_ready']}")

        # Run interactive session
        await interactive_query_session(query_engine)

    except Exception as e:
        print(f"❌ Error in Query Engine: {e}")
        return

if __name__ == "__main__":
    # Run the Query Engine
    asyncio.run(main())
