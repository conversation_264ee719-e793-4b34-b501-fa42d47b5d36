import os
import sys
import json
import hashlib
import asyncio
import aiofiles
import base64
import requests
import time
from pathlib import Path
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor
import openai
from dotenv import load_dotenv

# Simple JINA parsing - no external dependencies

# Load environment variables
load_dotenv()

# OpenAI client configuration
client = openai.OpenAI(
    api_key=os.getenv("AGENTIC_API_KEY"),
    base_url=os.getenv("AGENTIC_BASE_URL")
)

# Cache directory for parsed PDFs
CACHE_DIR = Path(__file__).parent.parent / "cached_data"
CACHE_DIR.mkdir(exist_ok=True)

def get_file_hash(file_path: str) -> str:
    """Generate hash for a file to use as cache key."""
    with open(file_path, 'rb') as f:
        return hashlib.md5(f.read()).hexdigest()

def get_cache_path(pdf_path: str) -> Path:
    """Get cache file path for a PDF."""
    file_hash = get_file_hash(pdf_path)
    filename = Path(pdf_path).stem
    return CACHE_DIR / f"{filename}_{file_hash}.txt"

async def get_cached_content(pdf_path: str) -> str:
    """Get cached content if available."""
    cache_path = get_cache_path(pdf_path)
    if cache_path.exists():
        print(f"✓ Using cached content for {Path(pdf_path).name}")
        async with aiofiles.open(cache_path, 'r', encoding='utf-8') as f:
            return await f.read()
    return None

async def cache_content(pdf_path: str, content: str):
    """Cache the parsed content."""
    cache_path = get_cache_path(pdf_path)
    async with aiofiles.open(cache_path, 'w', encoding='utf-8') as f:
        await f.write(content)
    print(f"✓ Cached content for {Path(pdf_path).name}")

def parse_pdf_with_jina(pdf_path: str, max_retries: int = 3) -> str:
    """Parse entire PDF using JINA AI Reader API with retry logic."""
    print(f"📄 Parsing {Path(pdf_path).name} with JINA...")
    
    # Read PDF file as binary once
    try:
        with open(pdf_path, 'rb') as f:
            pdf_bytes = f.read()
    except Exception as e:
        raise ValueError(f"Failed to read PDF file: {str(e)}")
    
    # Check file size (JINA has limits)
    file_size_mb = len(pdf_bytes) / (1024 * 1024)
    if file_size_mb > 50:  # 50MB limit
        print(f"⚠️  Large file detected ({file_size_mb:.1f}MB) - this may take longer")
    
    # Encode to base64
    pdf_base64 = base64.b64encode(pdf_bytes).decode('utf-8')
    
    # Prepare headers
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'QuanteraLatticeAgent/1.0'
    }
    
    # Add API key if available
    jina_api_key = os.getenv("JINA_API_KEY")
    if jina_api_key:
        headers["Authorization"] = f"Bearer {jina_api_key}"
    
    # Prepare payload for entire document
    payload = {
        "url": None,
        "pdf": pdf_base64,
        "filename": Path(pdf_path).name
    }
    
    # Retry logic with exponential backoff
    for attempt in range(max_retries):
        try:
            # Calculate timeout based on file size (minimum 60s, max 300s)
            timeout = min(300, max(60, int(file_size_mb * 10)))
            
            print(f"🔄 Attempt {attempt + 1}/{max_retries} (timeout: {timeout}s)")
            
            # Make API request
            response = requests.post(
                "https://r.jina.ai/",
                json=payload,
                headers=headers,
                timeout=timeout
            )
            
            if response.status_code == 200:
                content = response.text.strip()
                
                # Check if we got a valid response
                if content and len(content) > 50 and "Access denied" not in content and "Cloudflare" not in content:
                    print(f"✓ Successfully parsed {Path(pdf_path).name} ({len(content)} characters)")
                    return content
                else:
                    raise ValueError("Invalid or empty response from JINA API")
            
            elif response.status_code == 503:
                # Service temporarily unavailable
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + 1  # Exponential backoff: 2, 5, 9 seconds
                    print(f"⚠️  JINA API temporarily unavailable (503). Retrying in {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                else:
                    raise ValueError(f"JINA API persistently unavailable: {response.status_code} - {response.text}")
            
            elif response.status_code == 429:
                # Rate limited
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) * 3  # Longer wait for rate limits: 6, 12, 24 seconds
                    print(f"⚠️  Rate limited (429). Retrying in {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                else:
                    raise ValueError(f"JINA API rate limit exceeded: {response.status_code} - {response.text}")
            
            elif response.status_code in [502, 504]:
                # Bad gateway or gateway timeout
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + 2  # 3, 6, 10 seconds
                    print(f"⚠️  Gateway error ({response.status_code}). Retrying in {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                else:
                    raise ValueError(f"JINA API gateway error: {response.status_code} - {response.text}")
            
            else:
                # Other HTTP errors
                raise ValueError(f"JINA API error: {response.status_code} - {response.text}")
        
        except requests.exceptions.RequestException as e:
            # Network/connection errors
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + 1
                print(f"⚠️  Connection error: {str(e)}. Retrying in {wait_time}s...")
                time.sleep(wait_time)
                continue
            else:
                raise ValueError(f"Network error after {max_retries} attempts: {str(e)}")
        
        except Exception as e:
            # Other unexpected errors
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + 1
                print(f"⚠️  Unexpected error: {str(e)}. Retrying in {wait_time}s...")
                time.sleep(wait_time)
                continue
            else:
                raise ValueError(f"Failed to parse PDF after {max_retries} attempts: {str(e)}")
    
    # This should never be reached due to the exception handling above
    raise ValueError(f"Failed to parse PDF with JINA after {max_retries} attempts")

async def get_or_parse_pdf_content(pdf_path: str) -> str:
    """Get PDF content from cache or parse with JINA."""
    # Check cache first
    cached_content = await get_cached_content(pdf_path)
    if cached_content:
        return cached_content
    
    # Parse with JINA in thread pool (since it's synchronous)
    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor() as executor:
        content = await loop.run_in_executor(executor, parse_pdf_with_jina, pdf_path)
    
    # Cache the result
    await cache_content(pdf_path, content)
    return content

async def extract_headers_from_document(pdf_path: str, content: str) -> Dict[str, Any]:
    """Extract headers from document content using OpenAI."""
    filename = Path(pdf_path).name
    print(f"🤖 Extracting headers from {filename}...")
    
    prompt = f"""
You are an expert document analyst. Your task is to analyze the provided document content and extract potential HEADERS that could be used as column names in an Excel file.

Document Content:
{content[:]}  # Truncate to avoid token limits

Instructions:
1. Identify meaningful headers/column names that represent key data points, KPIs, metrics, or important information categories from this document
2. Headers should be:
   - Specific enough to be meaningful
   - General enough to be reusable across similar documents
   - Professional and clear
   - Suitable as Excel column headers

3. Consider different types of headers:
   - Financial metrics (e.g., "Total Revenue", "Net Income", "EBITDA")
   - Dates and periods (e.g., "Reporting Period", "Filing Date")
   - Company information (e.g., "Company Name", "Ticker Symbol")
   - Performance indicators (e.g., "Growth Rate", "Market Share")
   - Legal/regulatory items (e.g., "Compliance Status", "Audit Opinion")
   - Operational metrics (e.g., "Employee Count", "Customer Base")

4. For resumes: Name, Experience Years, Skills, Education, etc.
5. For contracts: Contract Value, Effective Date, Parties, Terms, etc.
6. For reports: Report Type, Author, Key Findings, Recommendations, etc.

CRITICAL: You must respond with ONLY a valid JSON object in this exact format:
{{
    "headers": ["Header 1", "Header 2", "Header 3", ...]
}}

No other text, explanations, or formatting. Just the JSON.
"""

    try:
        response = await asyncio.to_thread(
            client.chat.completions.create,
            model="agentic-large",
            messages=[
                {"role": "system", "content": "You are a professional document analyst. Always respond with valid JSON only."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=1000
        )
        
        response_text = response.choices[0].message.content.strip()
        
        # Parse JSON response
        try:
            result = json.loads(response_text)
            if "headers" in result and isinstance(result["headers"], list):
                print(f"✓ Extracted {len(result['headers'])} headers from {filename}")
                return {
                    "filename": filename,
                    "pdf_path": pdf_path,
                    "headers": result["headers"],
                    "success": True,
                    "error": None
                }
            else:
                raise ValueError("Invalid JSON structure")
        except json.JSONDecodeError as e:
            print(f"✗ JSON parsing error for {filename}: {e}")
            print(f"Response was: {response_text[:200]}...")
            return {
                "filename": filename,
                "pdf_path": pdf_path,
                "headers": [],
                "success": False,
                "error": f"JSON parsing error: {e}"
            }
    
    except Exception as e:
        print(f"✗ Error extracting headers from {filename}: {e}")
        return {
            "filename": filename,
            "pdf_path": pdf_path,
            "headers": [],
            "success": False,
            "error": str(e)
        }

async def parse_single_pdf_content(pdf_path: str) -> Dict[str, Any]:
    """Parse a single PDF and return content."""
    try:
        # Get or parse PDF content
        content = await get_or_parse_pdf_content(pdf_path)
        
        return {
            "filename": Path(pdf_path).name,
            "pdf_path": pdf_path,
            "content": content,
            "success": True,
            "error": None
        }
    
    except Exception as e:
        filename = Path(pdf_path).name
        print(f"✗ Error parsing {filename}: {e}")
        return {
            "filename": filename,
            "pdf_path": pdf_path,
            "content": None,
            "success": False,
            "error": str(e)
        }

async def extract_headers_for_parsed_content(parsed_content: Dict[str, Any]) -> Dict[str, Any]:
    """Extract headers from already parsed content."""
    if not parsed_content["success"] or not parsed_content["content"]:
        # Return the original error result
        return {
            "filename": parsed_content["filename"],
            "pdf_path": parsed_content["pdf_path"],
            "headers": [],
            "success": False,
            "error": parsed_content["error"]
        }
    
    try:
        # Extract headers using OpenAI
        result = await extract_headers_from_document(
            parsed_content["pdf_path"], 
            parsed_content["content"]
        )
        return result
    
    except Exception as e:
        print(f"✗ Error extracting headers from {parsed_content['filename']}: {e}")
        return {
            "filename": parsed_content["filename"],
            "pdf_path": parsed_content["pdf_path"],
            "headers": [],
            "success": False,
            "error": str(e)
        }

async def reduce_headers_to_final_set(individual_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Perform reduction of all individual headers into a final consolidated set.
    Uses LLM to analyze all headers and create a balanced, comprehensive set.
    """
    print("🔄 PHASE 3: Reducing headers to final consolidated set...")
    print("-" * 60)
    
    # Collect all successful headers
    all_headers = []
    successful_docs = []
    
    for result in individual_results:
        if result["success"] and result["headers"]:
            all_headers.extend(result["headers"])
            successful_docs.append({
                "filename": result["filename"],
                "headers": result["headers"]
            })
    
    if not all_headers:
        print("⚠️  No headers to reduce - no successful extractions found")
        return {
            "final_headers": [],
            "success": False,
            "error": "No headers available for reduction",
            "individual_docs_processed": len(individual_results),
            "successful_docs": 0
        }
    
    print(f"📊 Collected {len(all_headers)} total headers from {len(successful_docs)} documents")
    
    # Create detailed document summary for the reduction prompt
    doc_summary = ""
    for i, doc in enumerate(successful_docs, 1):
        doc_summary += f"\nDocument {i}: {doc['filename']}\n"
        doc_summary += f"Headers ({len(doc['headers'])}): {', '.join(doc['headers'])}\n"
    
    # Create reduction prompt
    reduction_prompt = f"""
You are an expert data analyst tasked with creating a FINAL CONSOLIDATED set of lattice headers for data extraction across multiple documents.

CONTEXT:
- You have been provided headers extracted from {len(successful_docs)} different documents
- These headers will be used to extract data from ALL documents in a collection
- The goal is to find the optimal balance: comprehensive enough to capture key information, but not so specific that only 1-2 documents would have relevant data

DOCUMENT HEADERS ANALYSIS:
{doc_summary}

ALL COLLECTED HEADERS ({len(all_headers)} total):
{', '.join(sorted(set(all_headers)))}

REDUCTION CRITERIA:
1. **Relevance Threshold**: Include headers that would likely have data in at least 30-40% of documents
2. **Comprehensiveness**: Cover major categories like:
   - Company/Entity identification
   - Financial metrics and performance
   - Dates and time periods
   - Legal/Regulatory information
   - Operational metrics
   - Strategic information
3. **Generalization**: Prefer broader terms over very specific ones
   - Good: "Total Revenue" (applicable to most financial docs)
   - Bad: "Q3 2023 Widget Sales" (too specific)
4. **Professional Standards**: Use clear, professional terminology suitable for business analysis
5. **Avoid Redundancy**: Don't include multiple headers that capture the same information

INSTRUCTIONS:
1. Analyze all the provided headers
2. Identify common themes and patterns
3. Create a consolidated set of 15-25 headers that:
   - Would be applicable across most document types in this collection
   - Cover the most important data points for business analysis
   - Are generic enough to extract meaningful data from diverse documents
   - Maintain professional clarity and specificity

CRITICAL: You must respond with ONLY a valid JSON object in this exact format:
{{
    "final_headers": ["Header 1", "Header 2", "Header 3", ...],
    "reasoning": "Brief explanation of the consolidation approach and key decisions made"
}}

No other text, explanations, or formatting. Just the JSON.
"""

    try:
        print("🤖 Sending reduction request to LLM...")
        
        response = await asyncio.to_thread(
            client.chat.completions.create,
            model="agentic-large",
            messages=[
                {"role": "system", "content": "You are a professional data analyst specializing in document header consolidation. Always respond with valid JSON only."},
                {"role": "user", "content": reduction_prompt}
            ],
            temperature=0.2,  # Lower temperature for more consistent results
            max_tokens=2000
        )
        
        response_text = response.choices[0].message.content.strip()
        
        # Parse JSON response
        try:
            result = json.loads(response_text)
            if "final_headers" in result and isinstance(result["final_headers"], list):
                final_headers = result["final_headers"]
                reasoning = result.get("reasoning", "No reasoning provided")
                
                print(f"✅ Successfully reduced to {len(final_headers)} final headers")
                print(f"💡 LLM Reasoning: {reasoning}")
                
                return {
                    "final_headers": final_headers,
                    "reasoning": reasoning,
                    "success": True,
                    "error": None,
                    "individual_docs_processed": len(individual_results),
                    "successful_docs": len(successful_docs),
                    "total_individual_headers": len(all_headers),
                    "unique_individual_headers": len(set(all_headers))
                }
            else:
                raise ValueError("Invalid JSON structure - missing 'final_headers' list")
                
        except json.JSONDecodeError as e:
            print(f"✗ JSON parsing error in reduction: {e}")
            print(f"Response was: {response_text[:300]}...")
            return {
                "final_headers": [],
                "success": False,
                "error": f"JSON parsing error: {e}",
                "individual_docs_processed": len(individual_results),
                "successful_docs": len(successful_docs)
            }
    
    except Exception as e:
        print(f"✗ Error in header reduction: {e}")
        return {
            "final_headers": [],
            "success": False,
            "error": str(e), 
            "individual_docs_processed": len(individual_results),
            "successful_docs": len(successful_docs)
        }

async def extract_lattice_headers(pdf_paths: List[str]) -> Dict[str, Any]:
    """
    Main function to extract lattice headers from multiple PDFs.
    Now includes a reduction step to consolidate all headers into a final set.
    """
    print(f"🚀 Starting header extraction for {len(pdf_paths)} documents...")
    print("=" * 60)
    
    # Validate PDF paths
    valid_paths = []
    for pdf_path in pdf_paths:
        if Path(pdf_path).exists():
            valid_paths.append(pdf_path)
        else:
            print(f"⚠️  File not found: {pdf_path}")
    
    if not valid_paths:
        print("❌ No valid PDF files found!")
        return {
            "individual_results": [],
            "final_headers_result": {
                "final_headers": [],
                "success": False,
                "error": "No valid PDF files found"
            }
        }
    
    print(f"📂 Processing {len(valid_paths)} valid PDF files...")
    
    # PHASE 1: Parse all PDFs with JINA first
    print("📄 PHASE 1: Parsing all PDFs with JINA...")
    print("-" * 50)
    
    # Parse PDFs with some delay to avoid overwhelming JINA API
    # For small batches (<=3), process in parallel
    # For larger batches, process in smaller chunks with delays
    if len(valid_paths) <= 3:
        print("🔄 Parsing all PDFs in parallel...")
        parse_tasks = [parse_single_pdf_content(pdf_path) for pdf_path in valid_paths]
        parsed_results = await asyncio.gather(*parse_tasks, return_exceptions=True)
    else:
        print(f"🔄 Parsing PDFs in batches to avoid API rate limits...")
        parsed_results = []
        batch_size = 2  # Process 2 PDFs at a time
        
        for i in range(0, len(valid_paths), batch_size):
            batch = valid_paths[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(valid_paths) + batch_size - 1) // batch_size
            
            print(f"📦 Parsing batch {batch_num}/{total_batches} ({len(batch)} PDFs)...")
            
            # Parse current batch in parallel
            parse_tasks = [parse_single_pdf_content(pdf_path) for pdf_path in batch]
            batch_results = await asyncio.gather(*parse_tasks, return_exceptions=True)
            parsed_results.extend(batch_results)
            
            # Add delay between batches (except for last batch)
            if i + batch_size < len(valid_paths):
                delay = 3  # 3 seconds between batches
                print(f"⏳ Waiting {delay}s before next batch...")
                await asyncio.sleep(delay)
    
    # Handle parsing exceptions
    parsed_contents = []
    for i, result in enumerate(parsed_results):
        if isinstance(result, Exception):
            filename = Path(valid_paths[i]).name
            print(f"✗ Exception parsing {filename}: {result}")
            parsed_contents.append({
                "filename": filename,
                "pdf_path": valid_paths[i],
                "content": None,
                "success": False,
                "error": str(result)
            })
        else:
            parsed_contents.append(result)
    
    # Count successful parses
    successful_parses = sum(1 for content in parsed_contents if content["success"])
    print(f"\n✅ PHASE 1 Complete: {successful_parses}/{len(parsed_contents)} PDFs parsed successfully")
    
    # PHASE 2: Extract headers from all parsed content in parallel
    print("\n🤖 PHASE 2: Extracting headers with OpenAI (all in parallel)...")
    print("-" * 60)
    
    # Extract headers for all parsed content in parallel
    individual_results = []
    if successful_parses > 0:
        print(f"🚀 Sending {successful_parses} OpenAI requests in parallel...")
        header_tasks = [extract_headers_for_parsed_content(content) for content in parsed_contents]
        final_results = await asyncio.gather(*header_tasks, return_exceptions=True)
        
        # Handle header extraction exceptions
        for i, result in enumerate(final_results):
            if isinstance(result, Exception):
                content = parsed_contents[i]
                print(f"✗ Exception extracting headers from {content['filename']}: {result}")
                individual_results.append({
                    "filename": content["filename"],
                    "pdf_path": content["pdf_path"],
                    "headers": [],
                    "success": False,
                    "error": str(result)
                })
            else:
                individual_results.append(result)
        
        successful_extractions = sum(1 for r in individual_results if r["success"])
        print(f"\n✅ PHASE 2 Complete: {successful_extractions}/{len(individual_results)} header extractions successful")
    else:
        print("⚠️  No successfully parsed PDFs - skipping header extraction")
        individual_results = parsed_contents
    
    # PHASE 3: Reduce all headers to final consolidated set
    print("\n🧠 PHASE 3: Consolidating headers into final set...")
    print("-" * 60)
    
    final_headers_result = await reduce_headers_to_final_set(individual_results)
    
    print(f"\n🎯 EXTRACTION PIPELINE COMPLETE!")
    print("=" * 60)
    
    return {
        "individual_results": individual_results,
        "final_headers_result": final_headers_result
    }

def print_results(results: Dict[str, Any]):
    """Print the extracted headers alongside document names and final consolidated headers."""
    individual_results = results["individual_results"]
    final_headers_result = results["final_headers_result"]
    
    print("\n" + "=" * 60)
    print("📊 INDIVIDUAL DOCUMENT RESULTS")
    print("=" * 60)
    
    successful_count = sum(1 for r in individual_results if r["success"])
    print(f"✅ Successfully processed: {successful_count}/{len(individual_results)} documents\n")
    
    for result in individual_results:
        filename = result["filename"]
        print(f"📄 {filename}")
        print("-" * len(filename))
        
        if result["success"]:
            headers = result["headers"]
            if headers:
                for i, header in enumerate(headers, 1):
                    print(f"  {i:2d}. {header}")
                print(f"     → Total headers: {len(headers)}")
            else:
                print("     → No headers extracted")
        else:
            print(f"     ❌ Error: {result['error']}")
        
        print()
    
    # Print final consolidated headers
    print("=" * 60)
    print("🎯 FINAL CONSOLIDATED LATTICE HEADERS")
    print("=" * 60)
    
    if final_headers_result["success"]:
        final_headers = final_headers_result["final_headers"]
        print(f"✅ Successfully consolidated into {len(final_headers)} final headers")
        print(f"📊 Processed {final_headers_result['successful_docs']}/{final_headers_result['individual_docs_processed']} documents")
        print(f"🔢 Reduced from {final_headers_result['unique_individual_headers']} unique individual headers")
        
        if final_headers_result.get("reasoning"):
            print(f"💡 LLM Reasoning: {final_headers_result['reasoning']}")
        
        print("\n🏷️  FINAL LATTICE HEADERS:")
        print("-" * 30)
        for i, header in enumerate(final_headers, 1):
            print(f"  {i:2d}. {header}")
        
        print(f"\n📋 Total Final Headers: {len(final_headers)}")
    else:
        print(f"❌ Failed to create final consolidated headers")
        print(f"Error: {final_headers_result['error']}")
    
    print("\n" + "=" * 60)

# Main execution function
async def main():
    """Main execution function for testing."""
        # Example usage - replace with actual PDF paths
    sample_pdf_paths = [
        # MP Materials examples
        "../../mp_materials/pdfs/form-10-k.pdf",
        "../../mp_materials/pdfs/form-10-q.pdf", 
        "../../mp_materials/pdfs/contract.pdf",
        
        # Resido examples  
        "../../resido/pdfs/form-10-k.pdf",
        "../../resido/pdfs/annual-reports.pdf",
        "../../resido/pdfs/environmental-agreement.pdf",
    ]
    
    # Extract headers
    results = await extract_lattice_headers(sample_pdf_paths)
    
    # Print results
    print_results(results)
    
    return results

if __name__ == "__main__":
    # Run the script
    asyncio.run(main())
