import os
import json
import faiss
import requests
import numpy as np
import time
import hashlib
import asyncio
import aiofiles
import base64
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from nltk.tokenize import word_tokenize
from rank_bm25 import BM25<PERSON>ka<PERSON>
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
JINA_API_URL = os.getenv("JINA_API_URL", "https://api.jina.ai/v1/embeddings")
JINA_API_KEY = os.getenv("JINA_API_KEY")
MODEL_NAME = os.getenv("MODEL_NAME")

# Initialize OpenAI client
client = OpenAI(
    base_url=os.getenv("AGENTIC_BASE_URL"),
    api_key=os.getenv("AGENTIC_API_KEY")
)

# Cache directory for parsed PDFs (same as latticeheader_agent)
CACHE_DIR = Path(__file__).parent.parent / "cached_data"
CACHE_DIR.mkdir(exist_ok=True)

### --------------------- PDF PARSING & CACHING ---------------------

def get_file_hash(file_path: str) -> str:
    """Generate hash for a file to use as cache key."""
    with open(file_path, 'rb') as f:
        return hashlib.md5(f.read()).hexdigest()

def get_cache_path(pdf_path: str) -> Path:
    """Get cache file path for a PDF."""
    file_hash = get_file_hash(pdf_path)
    filename = Path(pdf_path).stem
    return CACHE_DIR / f"{filename}_{file_hash}.txt"

async def get_cached_content(pdf_path: str) -> str:
    """Get cached content if available."""
    cache_path = get_cache_path(pdf_path)
    if cache_path.exists():
        print(f"✓ Using cached content for {Path(pdf_path).name}")
        async with aiofiles.open(cache_path, 'r', encoding='utf-8') as f:
            return await f.read()
    return None

async def cache_content(pdf_path: str, content: str):
    """Cache the parsed content."""
    cache_path = get_cache_path(pdf_path)
    async with aiofiles.open(cache_path, 'w', encoding='utf-8') as f:
        await f.write(content)
    print(f"✓ Cached content for {Path(pdf_path).name}")

def parse_pdf_with_jina(pdf_path: str, max_retries: int = 3) -> str:
    """Parse entire PDF using JINA AI Reader API with retry logic."""
    print(f"📄 Parsing {Path(pdf_path).name} with JINA...")
    
    # Read PDF file as binary once
    try:
        with open(pdf_path, 'rb') as f:
            pdf_bytes = f.read()
    except Exception as e:
        raise ValueError(f"Failed to read PDF file: {str(e)}")
    
    # Check file size (JINA has limits)
    file_size_mb = len(pdf_bytes) / (1024 * 1024)
    if file_size_mb > 50:  # 50MB limit
        print(f"⚠️  Large file detected ({file_size_mb:.1f}MB) - this may take longer")
    
    # Encode to base64
    pdf_base64 = base64.b64encode(pdf_bytes).decode('utf-8')
    
    # Prepare headers
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'QuanteraHybridRAG/1.0'
    }
    
    # Add API key if available
    jina_api_key = os.getenv("JINA_API_KEY")
    if jina_api_key:
        headers["Authorization"] = f"Bearer {jina_api_key}"
    
    # Prepare payload for entire document
    payload = {
        "url": None,
        "pdf": pdf_base64,
        "filename": Path(pdf_path).name
    }
    
    # Retry logic with exponential backoff
    for attempt in range(max_retries):
        try:
            # Calculate timeout based on file size (minimum 60s, max 300s)
            timeout = min(300, max(60, int(file_size_mb * 10)))
            
            print(f"🔄 Attempt {attempt + 1}/{max_retries} (timeout: {timeout}s)")
            
            # Make API request
            response = requests.post(
                "https://r.jina.ai/",
                json=payload,
                headers=headers,
                timeout=timeout
            )
            
            if response.status_code == 200:
                content = response.text.strip()
                
                # Check if we got a valid response
                if content and len(content) > 50 and "Access denied" not in content and "Cloudflare" not in content:
                    print(f"✓ Successfully parsed {Path(pdf_path).name} ({len(content)} characters)")
                    return content
                else:
                    raise ValueError("Invalid or empty response from JINA API")
            
            elif response.status_code == 503:
                # Service temporarily unavailable
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + 1  # Exponential backoff: 2, 5, 9 seconds
                    print(f"⚠️  JINA API temporarily unavailable (503). Retrying in {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                else:
                    raise ValueError(f"JINA API persistently unavailable: {response.status_code} - {response.text}")
            
            elif response.status_code == 429:
                # Rate limited
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) * 3  # Longer wait for rate limits: 6, 12, 24 seconds
                    print(f"⚠️  Rate limited (429). Retrying in {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                else:
                    raise ValueError(f"JINA API rate limit exceeded: {response.status_code} - {response.text}")
            
            elif response.status_code in [502, 504]:
                # Bad gateway or gateway timeout
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + 2  # 3, 6, 10 seconds
                    print(f"⚠️  Gateway error ({response.status_code}). Retrying in {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                else:
                    raise ValueError(f"JINA API gateway error: {response.status_code} - {response.text}")
            
            else:
                # Other HTTP errors
                raise ValueError(f"JINA API error: {response.status_code} - {response.text}")
        
        except requests.exceptions.RequestException as e:
            # Network/connection errors
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + 1
                print(f"⚠️  Connection error: {str(e)}. Retrying in {wait_time}s...")
                time.sleep(wait_time)
                continue
            else:
                raise ValueError(f"Network error after {max_retries} attempts: {str(e)}")
        
        except Exception as e:
            # Other unexpected errors
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + 1
                print(f"⚠️  Unexpected error: {str(e)}. Retrying in {wait_time}s...")
                time.sleep(wait_time)
                continue
            else:
                raise ValueError(f"Failed to parse PDF after {max_retries} attempts: {str(e)}")
    
    # This should never be reached due to the exception handling above
    raise ValueError(f"Failed to parse PDF with JINA after {max_retries} attempts")

async def get_or_parse_pdf_content(pdf_path: str) -> str:
    """Get PDF content from cache or parse with JINA."""
    # Check cache first
    cached_content = await get_cached_content(pdf_path)
    if cached_content:
        return cached_content
    
    # Parse with JINA in thread pool (since it's synchronous)
    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor() as executor:
        content = await loop.run_in_executor(executor, parse_pdf_with_jina, pdf_path)
    
    # Cache the result
    await cache_content(pdf_path, content)
    return content

### --------------------- CHUNKING ---------------------

def calculate_tokens(text: str) -> int:
    """Calculate approximate tokens using 4 characters = 1 token."""
    return len(text) // 4

def chunk_text(text: str, max_tokens: int = 7000) -> list:
    """
    Chunk text into segments of maximum token count.
    Uses simple sentence-based chunking with overlap.
    """
    print(f"Chunking text of {len(text)} characters (~{calculate_tokens(text)} tokens)")
    
    max_chars = max_tokens * 4  # 4 chars = 1 token
    
    # Split by sentences for better chunk boundaries
    sentences = text.split('. ')
    chunks = []
    current_chunk = ""
    
    for sentence in sentences:
        # Add sentence with period back
        sentence_with_period = sentence + '. '
        
        # Check if adding this sentence would exceed limit
        if calculate_tokens(current_chunk + sentence_with_period) > max_tokens:
            if current_chunk:  # Only add non-empty chunks
                chunks.append(current_chunk.strip())
                current_chunk = sentence_with_period
            else:
                # If single sentence is too long, force split by characters
                if len(sentence_with_period) > max_chars:
                    for i in range(0, len(sentence_with_period), max_chars):
                        chunk_part = sentence_with_period[i:i + max_chars]
                        if chunk_part.strip():
                            chunks.append(chunk_part.strip())
                else:
                    current_chunk = sentence_with_period
        else:
            current_chunk += sentence_with_period
    
    # Add remaining chunk
    if current_chunk.strip():
        chunks.append(current_chunk.strip())
    
    print(f"Created {len(chunks)} chunks")
    
    # Print chunk statistics
    chunk_sizes = [calculate_tokens(chunk) for chunk in chunks]
    print(f"Chunk token sizes - Min: {min(chunk_sizes)}, Max: {max(chunk_sizes)}, Avg: {sum(chunk_sizes)/len(chunk_sizes):.1f}")
    
    return chunks

### --------------------- DOCUMENT LOADING ---------------------

async def load_and_process_pdf(pdf_path: str) -> tuple:
    """
    Load a PDF, parse it, and chunk it into manageable pieces.
    Returns chunks and chunk metadata.
    """
    print(f"Loading and processing PDF: {pdf_path}")
    
    # Validate PDF path
    if not Path(pdf_path).exists():
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")
    
    # Get or parse PDF content
    content = await get_or_parse_pdf_content(pdf_path)
    
    # Chunk the content
    chunks = chunk_text(content)
    
    # Create chunk IDs
    chunk_ids = [f"chunk_{i}" for i in range(len(chunks))]
    
    # Create document metadata
    document_info = {
        "file_name": Path(pdf_path).name,
        "file_path": pdf_path,
        "total_chunks": len(chunks),
        "total_content_length": len(content),
        "total_tokens": calculate_tokens(content)
    }
    
    print(f"Successfully processed {document_info['file_name']}: {len(chunks)} chunks, ~{document_info['total_tokens']} tokens")
    
    return chunks, chunk_ids, document_info

### --------------------- EMBEDDINGS ---------------------

def get_jina_embeddings(texts, batch_size=1000, max_retries=3, delay=2):
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {JINA_API_KEY}"
    }
    
    # Process texts in batches due to API limit
    all_embeddings = []
    total_batches = (len(texts) + batch_size - 1) // batch_size
    
    print(f"Processing {len(texts)} texts in {total_batches} batches")
    
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i + batch_size]
        batch_num = i // batch_size + 1
        
        print(f"Processing batch {batch_num}/{total_batches} ({len(batch_texts)} texts)")
        
        # Retry mechanism for API calls
        for attempt in range(max_retries):
            try:
                data = {
                    "model": "jina-embeddings-v3",
                    "task": "text-matching",
                    "input": batch_texts
                }
                
                response = requests.post(JINA_API_URL, headers=headers, json=data)
                
                if response.status_code == 200:
                    batch_embeddings = response.json()["data"]
                    all_embeddings.extend([e["embedding"] for e in batch_embeddings])
                    
                    print(f"Successfully processed batch {batch_num}/{total_batches}")
                    break
                else:
                    print(f"API request failed with status {response.status_code}")
                    print(f"Response: {response.text}")
                    if attempt < max_retries - 1:
                        print(f"Retrying in {delay} seconds (attempt {attempt + 1}/{max_retries})")
                        time.sleep(delay)
                        delay *= 2  # Exponential backoff
                    else:
                        response.raise_for_status()
                        
            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"Exception occurred: {e}")
                    print(f"Retrying in {delay} seconds (attempt {attempt + 1}/{max_retries})")
                    time.sleep(delay)
                    delay *= 2  # Exponential backoff
                else:
                    raise
    
    print(f"Successfully got embeddings for {len(all_embeddings)} texts")
    return np.array(all_embeddings).astype("float32")

### --------------------- INDEXERS ---------------------

def build_dense_index(chunks):
    print("Building dense (FAISS) index with Jina embeddings")
    
    embeddings = get_jina_embeddings(chunks)
    
    dim = embeddings.shape[1]
    index = faiss.IndexIDMap(faiss.IndexFlatL2(dim))
    ids = np.arange(len(chunks))
    
    index.add_with_ids(embeddings, ids)
    print(f"Dense index built successfully with {index.ntotal} vectors")
    
    return index

def build_sparse_index(chunks):
    print("Building sparse (BM25) index")
    
    tokenized = [word_tokenize(chunk.lower()) for chunk in chunks]
    
    bm25 = BM25Okapi(tokenized)
    print(f"Sparse index built successfully with {len(tokenized)} documents")
    
    return bm25

### --------------------- HYBRID QUERY ---------------------

def hybrid_query(query, bm25, faiss_index, chunks, top_k=5, rrf=False):
    print(f"Processing query: {query}")

    # Sparse BM25 search
    tokenized_query = word_tokenize(query.lower())
    bm25_scores = bm25.get_scores(tokenized_query)
    bm25_top_ids = np.argsort(bm25_scores)[::-1][:top_k]

    # Dense FAISS search
    query_vec = get_jina_embeddings([query])
    _, faiss_top_ids = faiss_index.search(query_vec, top_k)

    # Merge top unique IDs
    # Reciprocal Rank Fusion if enabled
    if rrf:
        print("Performing Reciprocal Rank Fusion")
        # Get rankings for BM25
        bm25_ranks = {idx: 1/(rank + 60) for rank, idx in enumerate(bm25_top_ids)}
        
        # Get rankings for FAISS
        faiss_ranks = {idx: 1/(rank + 60) for rank, idx in enumerate(faiss_top_ids[0])}
        
        # Combine all document IDs
        all_ids = set(bm25_ranks.keys()).union(set(faiss_ranks.keys()))
        
        # Calculate RRF scores
        rrf_scores = {}
        for doc_id in all_ids:
            rrf_scores[doc_id] = bm25_ranks.get(doc_id, 0) + faiss_ranks.get(doc_id, 0)
        
        # Sort by RRF scores and get top_k IDs
        combined_ids = sorted(rrf_scores.keys(), key=lambda x: rrf_scores[x], reverse=True)[:top_k]
        print(f"RRF fusion completed with {len(combined_ids)} results")
    else:
        combined_ids = set(bm25_top_ids).union(set(faiss_top_ids[0]))


    # Fetch results directly from chunks list
    results = []
    for idx in combined_ids:
        if idx < len(chunks):
            results.append((idx, chunks[idx]))

    print(f"Retrieved {len(results)} chunks for query")
    return results

### --------------------- GENERATION ---------------------

def generate_answer(query, retrieved_chunks, model="agentic-large"):
    print("Generating answer using LLM")
    
    # Format context from retrieved chunks
    context = "\n\n".join([chunk for _, chunk in retrieved_chunks])
    context_length = len(context)
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant. Answer questions based on the provided context."},
        {"role": "user", "content": f"""Context: {context}\n\nQuestion: {query}\n\nPlease provide a clear and concise answer based on the context above."""}
    ]

    try:
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=0.7,
            max_tokens=5000
        )
        
        answer = response.choices[0].message.content
        print(f"Answer generated successfully (length: {len(answer)} characters)")
        
        return answer
        
    except Exception as e:
        print(f"Error generating answer: {str(e)}")
        return "Sorry, I encountered an error generating the answer."

### --------------------- MAIN PIPELINE ---------------------

async def run_hybrid_rag_pipeline(pdf_path: str):
    """
    Initialize hybrid RAG pipeline with a single PDF document.
    """
    print("Starting hybrid RAG pipeline initialization")
    
    # Load and process the PDF
    chunks, chunk_ids, document_info = await load_and_process_pdf(pdf_path)
    
    print(f"Loaded {len(chunks)} chunks from {document_info['file_name']}")
    print(f"Document stats: {document_info['total_content_length']} chars, ~{document_info['total_tokens']} tokens")

    # Build indices
    faiss_index = build_dense_index(chunks)
    bm25_index = build_sparse_index(chunks)

    print("RAG pipeline initialization completed successfully")
    
    return lambda query: hybrid_query(query, bm25_index, faiss_index, chunks), document_info

### --------------------- ENTRY POINT ---------------------

async def main():
    """Main async entry point for the RAG system."""
    print("Starting RAG chatbot session")
    
    # Example PDF path - you can change this
    pdf_path = "../../mp_materials/pdfs/form-10-k.pdf"  # Update this path as needed
    
    try:
        query_rag, document_info = await run_hybrid_rag_pipeline(pdf_path)
        
        print(f"\n🤖 Welcome to the Interactive RAG Chatbot!")
        print(f"📄 Loaded document: {document_info['file_name']}")
        print(f"📊 Document contains {document_info['total_chunks']} chunks (~{document_info['total_tokens']} tokens)")
        
        # Test mode - run a sample query first
        print("\n🧪 Running test query...")
        test_query = "What is the company's revenue?"
        print(f"Test query: {test_query}")
        
        results = query_rag(test_query)
        answer = generate_answer(test_query, results)
        
        print("\n📄 Test Retrieved Chunks:")
        for idx, r in results[:2]:  # Show only first 2 chunks
            print(f"--- Chunk ID: {idx} ---\n{r[:300]}...\n")
        
        print("\n" + "="*50)
        print("💡 TEST ANSWER:")
        print("="*50)
        print(answer)
        print("="*50)
        
        print("\nPress 'Q' to quit or continue with interactive mode.\n")
        
        query_count = 0
        
        while True:
            # 🔍 Get user query
            user_query = input("\nEnter your query (Q to quit): ")
            
            if user_query.upper() == 'Q':
                print(f"Session ended. Processed {query_count} queries total")
                print("\n👋 Goodbye!")
                break
                
            query_count += 1
            print(f"Processing query #{query_count}: {user_query}")
            
            # Process query and generate response
            results = query_rag(user_query)
            answer = generate_answer(user_query, results)

            # Display results
            print("\n📄 Retrieved Chunks:\n")
            for idx, r in results:
                print(f"--- Chunk ID: {idx} ---\n{r[:500]}...\n")
            
            # Ensure answer is displayed
            print("\n" + "="*50)
            print("💡 ANSWER:")
            print("="*50)
            
            if answer:
                print(answer)
            else:
                print("⚠️ No answer generated or empty response")
                print("No answer was generated for the query")
                
            print("="*50)
    
    except Exception as e:
        print(f"❌ Error initializing RAG pipeline: {e}")
        return

if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
